using System;
using System.Data;
using DAL;
using Model;

namespace BLL
{
    /// <summary>
    /// ??????????????????
    /// </summary>
    public class CompanyPricingBll
    {
        private static readonly CompanyPricingDal dal = new CompanyPricingDal();

        /// <summary>
        /// ?????????????
        /// </summary>
        public static DataTable GetCompanyList(string compName, string legalMan, string category, string bDate, string eDate, int rows, int page, string companyNo)
        {
            return dal.GetCompanyList(compName, legalMan, category, bDate, eDate, rows, page, companyNo);
        }

        /// <summary>
        /// ?????????????????
        /// </summary>
        public static bool CheckCompanyNameExists(string compName, string companyNo, string excludeCompID = "")
        {
            return dal.CheckCompanyNameExists(compName, companyNo, excludeCompID);
        }

        /// <summary>
        /// ?????????
        /// </summary>
        public static string AddCompany(string compID, string compName, string nameJC, string compNameEN,
            string legalMan, string organizationCode, string category, string businessScope,
            string regAmount, string taxNumber, string prdXKZ, string regAddr,
            string compAddr, string pAddr, string postalCode, string isMember,
            string cMan, string sex, string phone, string fax, string email,
            string website, string appid, string appsecret, string companyNo,
            string status, string inMan, string remark)
        {
            var company = new CompanyInfo
            {
                CompID = compID,
                CompName = compName,
                NameJC = nameJC,
                CompNameEN = compNameEN,
                LegalMan = legalMan,
                OrganizationCode = organizationCode,
                Category = category,
                BusinessScope = businessScope,
                RegAmount = regAmount,
                TaxNumber = taxNumber,
                PrdXKZ = prdXKZ,
                RegAddr = regAddr,
                CompAddr = compAddr,
                PAddr = pAddr,
                PostalCode = postalCode,
                IsMember = isMember,
                CMan = cMan,
                Sex = sex,
                Phone = phone,
                Fax = fax,
                EMail = email,
                Website = website,
                APPID = appid,
                APPSECRET = appsecret,
                CompanyNo = companyNo,
                Status = "????", // ??????????????
                InMan = inMan,
                InDate = DateTime.Now,
                Remark = remark
            };

            return dal.AddCompany(company);
        }

        /// <summary>
        /// ?????????
        /// </summary>
        public static string UpdateCompany(string compID, string compName, string nameJC, string compNameEN,
            string legalMan, string organizationCode, string category, string businessScope,
            string regAmount, string taxNumber, string prdXKZ, string regAddr,
            string compAddr, string pAddr, string postalCode, string isMember,
            string cMan, string sex, string phone, string fax, string email,
            string website, string appid, string appsecret, string companyNo,
            string status, string inMan, string remark)
        {
            var company = new CompanyInfo
            {
                CompID = compID,
                CompName = compName,
                NameJC = nameJC,
                CompNameEN = compNameEN,
                LegalMan = legalMan,
                OrganizationCode = organizationCode,
                Category = category,
                BusinessScope = businessScope,
                RegAmount = regAmount,
                TaxNumber = taxNumber,
                PrdXKZ = prdXKZ,
                RegAddr = regAddr,
                CompAddr = compAddr,
                PAddr = pAddr,
                PostalCode = postalCode,
                IsMember = isMember,
                CMan = cMan,
                Sex = sex,
                Phone = phone,
                Fax = fax,
                EMail = email,
                Website = website,
                APPID = appid,
                APPSECRET = appsecret,
                CompanyNo = companyNo,
                Status = status,
                InMan = inMan,
                InDate = DateTime.Now,
                Remark = remark
            };

            return dal.UpdateCompany(company);
        }

        /// <summary>
        /// ????????
        /// </summary>
        public static string UpdateCompanyStatus(string compID, string status, string companyNo, string inMan)
        {
            var company = new CompanyInfo
            {
                CompID = compID,
                Status = status,
                CompanyNo = companyNo,
                InMan = inMan,
                InDate = DateTime.Now
            };

            return dal.UpdateCompany(company);
        }

        /// <summary>
        /// ?????????
        /// </summary>
        public static string DeleteCompany(string compID, string companyNo, string loginUser)
        {
            return dal.DeleteCompany(compID, companyNo, loginUser);
        }

        /// <summary>
        /// ??????????????
        /// </summary>
        public static bool CheckCompanyCanDelete(string compID, string companyNo)
        {
            return dal.CheckCompanyCanDelete(compID, companyNo);
        }

        /// <summary>
        /// ??????ID
        /// </summary>
        public static string CreateCompanyID()
        {
            string sNo;
            // ?????????ID
            string sMaxNo = dal.GetMaxCompanyID();
            if (string.IsNullOrEmpty(sMaxNo))
            {
                sNo = "C0001";
            }
            else
            {
                // ?????????????????????
                if (sMaxNo.Length == 5 && sMaxNo.StartsWith("C"))
                {
                    string sTemp = sMaxNo.Substring(1);
                    if (int.TryParse(sTemp, out int iMax))
                    {
                        iMax++;
                        sNo = "C" + iMax.ToString().PadLeft(4, '0');
                    }
                    else
                    {
                        sNo = "C0001";
                    }
                }
                else
                {
                    sNo = "C0001";
                }
            }
            return sNo;
        }

        /// <summary>
        /// 获取第一个客户信息（时间最早的客户）
        /// </summary>
        public static CompanyInfo GetFirstCustomerInfo(string companyNo)
        {
            return dal.GetFirstCustomerInfo(companyNo);
        }
    }
} 