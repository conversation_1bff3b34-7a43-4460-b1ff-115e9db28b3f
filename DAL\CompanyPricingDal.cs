using System;
using System.Data;
using System.Data.SqlClient;
using Common;
using Model;

namespace DAL
{
    /// <summary>
    /// 客户信息管理数据访问层
    /// </summary>
    public class CompanyPricingDal
    {
        /// <summary>
        /// 获取客户信息列表
        /// </summary>
        public DataTable GetCompanyList(string compName, string legalMan, string category, string bDate, string eDate, int rows, int page, string companyNo)
        {
            string sql = @"
                WITH TempTable AS (
                    SELECT ROW_NUMBER() OVER(ORDER BY InDate DESC) AS RowNum, 
                           CompID, CompName, NameJC, CompNameEN, LegalMan, OrganizationCode,
                           Category, BusinessScope, RegAmount, TaxNumber, PrdXKZ, RegAddr,
                           CompAddr, PAddr, PostalCode, IsMember, CMan, Sex, Phone, Fax,
                           EMail, Website, APPID, APPSECRET, Status, CompanyNo, InMan,
                           REPLACE(CONVERT(varchar(19), InDate, 120), 'T', ' ') as InDate,
                           Remark,
                           (SELECT COUNT(1) FROM T_CompanyInfo 
                            WHERE 1=1 
                            AND (CompName LIKE '%' + @CompName + '%' OR @CompName = '')
                            AND (LegalMan LIKE '%' + @LegalMan + '%' OR @LegalMan = '')
                            AND (Category LIKE '%' + @Category + '%' OR @Category = '')
                            AND (CONVERT(varchar(10), InDate, 120) >= @BDate OR @BDate = '')
                            AND (CONVERT(varchar(10), InDate, 120) <= @EDate OR @EDate = '')
                            AND CompanyNo = @CompanyNo) AS NumCount
                    FROM T_CompanyInfo
                    WHERE 1=1 
                    AND (CompName LIKE '%' + @CompName + '%' OR @CompName = '')
                    AND (LegalMan LIKE '%' + @LegalMan + '%' OR @LegalMan = '')
                    AND (Category LIKE '%' + @Category + '%' OR @Category = '')
                    AND (CONVERT(varchar(10), InDate, 120) >= @BDate OR @BDate = '')
                    AND (CONVERT(varchar(10), InDate, 120) <= @EDate OR @EDate = '')
                    AND CompanyNo = @CompanyNo
                )
                SELECT * FROM TempTable 
                WHERE RowNum BETWEEN (@Page - 1) * @Rows + 1 AND @Page * @Rows
                ORDER BY InDate DESC";

            SqlParameter[] parameters = {
                new SqlParameter("@CompName", SqlDbType.NVarChar, 200) { Value = compName ?? "" },
                new SqlParameter("@LegalMan", SqlDbType.NVarChar, 60) { Value = legalMan ?? "" },
                new SqlParameter("@Category", SqlDbType.NVarChar, 200) { Value = category ?? "" },
                new SqlParameter("@BDate", SqlDbType.NVarChar, 10) { Value = bDate ?? "" },
                new SqlParameter("@EDate", SqlDbType.NVarChar, 10) { Value = eDate ?? "" },
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar, 10) { Value = companyNo },
                new SqlParameter("@Rows", SqlDbType.Int) { Value = rows },
                new SqlParameter("@Page", SqlDbType.Int) { Value = page }
            };

            return DBHelper.GetDataTable(sql, CommandType.Text, parameters);
        }

        /// <summary>
        /// 检查公司名称是否已存在
        /// </summary>
        public bool CheckCompanyNameExists(string compName, string companyNo, string excludeCompID = "")
        {
            string sql = @"
                SELECT COUNT(1) FROM T_CompanyInfo 
                WHERE CompName = @CompName 
                AND CompanyNo = @CompanyNo
                AND (@ExcludeCompID = '' OR CompID != @ExcludeCompID)";

            SqlParameter[] parameters = {
                new SqlParameter("@CompName", SqlDbType.NVarChar) { Value = compName },
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo },
                new SqlParameter("@ExcludeCompID", SqlDbType.NVarChar) { Value = excludeCompID ?? "" }
            };

            try
            {
                int result = DBHelper.GetScalar(sql, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception("检查公司名称是否存在时出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 添加客户信息
        /// </summary>
        public string AddCompany(CompanyInfo company)
        {
            string sql = @"
                INSERT INTO T_CompanyInfo (
                    CompID, CompName, NameJC, CompNameEN, LegalMan, OrganizationCode,
                    Category, BusinessScope, RegAmount, TaxNumber, PrdXKZ, RegAddr,
                    CompAddr, PAddr, PostalCode, IsMember, CMan, Sex, Phone, Fax,
                    EMail, Website, APPID, APPSECRET, CompanyNo, Status, InMan, InDate, Remark
                ) VALUES (
                    @CompID, @CompName, @NameJC, @CompNameEN, @LegalMan, @OrganizationCode,
                    @Category, @BusinessScope, @RegAmount, @TaxNumber, @PrdXKZ, @RegAddr,
                    @CompAddr, @PAddr, @PostalCode, @IsMember, @CMan, @Sex, @Phone, @Fax,
                    @EMail, @Website, @APPID, @APPSECRET, @CompanyNo, @Status, @InMan, @InDate, @Remark
                )";

            try
            {
                int result = DBHelper.ExecuteCommand(sql, CreateParameters(company));
                return result > 0 ? "" : "添加失败";
            }
            catch (Exception ex)
            {
                return "添加失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 更新客户信息
        /// </summary>
        public string UpdateCompany(CompanyInfo company)
        {
            // 如果只有CompID和Status字段有值，说明是状态更新操作
            bool isStatusUpdate = !string.IsNullOrEmpty(company.Status) && 
                string.IsNullOrEmpty(company.CompName);

            string sql = isStatusUpdate ?
                @"UPDATE T_CompanyInfo SET
                    Status = @Status,
                    InMan = @InMan,
                    InDate = @InDate
                WHERE CompID = @CompID
                AND CompanyNo = @CompanyNo" :
                @"UPDATE T_CompanyInfo SET
                    CompName = @CompName,
                    NameJC = @NameJC,
                    CompNameEN = @CompNameEN,
                    LegalMan = @LegalMan,
                    OrganizationCode = @OrganizationCode,
                    Category = @Category,
                    BusinessScope = @BusinessScope,
                    RegAmount = @RegAmount,
                    TaxNumber = @TaxNumber,
                    PrdXKZ = @PrdXKZ,
                    RegAddr = @RegAddr,
                    CompAddr = @CompAddr,
                    PAddr = @PAddr,
                    PostalCode = @PostalCode,
                    IsMember = @IsMember,
                    CMan = @CMan,
                    Sex = @Sex,
                    Phone = @Phone,
                    Fax = @Fax,
                    EMail = @EMail,
                    Website = @Website,
                    APPID = @APPID,
                    APPSECRET = @APPSECRET,
                    Status = @Status,
                    InMan = @InMan,
                    InDate = @InDate,
                    Remark = @Remark
                WHERE CompID = @CompID
                AND CompanyNo = @CompanyNo";

            try
            {
                SqlParameter[] parameters = isStatusUpdate ? 
                    new SqlParameter[] {
                        new SqlParameter("@CompID", SqlDbType.NVarChar) { Value = company.CompID },
                        new SqlParameter("@Status", SqlDbType.NVarChar) { Value = company.Status },
                        new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = company.CompanyNo },
                        new SqlParameter("@InMan", SqlDbType.NVarChar) { Value = company.InMan },
                        new SqlParameter("@InDate", SqlDbType.DateTime) { Value = company.InDate }
                    } : 
                    CreateParameters(company);

                int result = DBHelper.ExecuteCommand(sql, parameters);
                return result > 0 ? "" : "更新失败";
            }
            catch (Exception ex)
            {
                return "更新失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 删除客户信息
        /// </summary>
        public string DeleteCompany(string compID, string companyNo, string loginUser)
        {
            string sql = @"
                DELETE FROM T_CompanyInfo 
                WHERE CompID = @CompID 
                AND CompanyNo = @CompanyNo";

            try
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@CompID", SqlDbType.NVarChar) { Value = compID },
                    new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo }
                };

                int result = DBHelper.ExecuteCommand(sql, parameters);
                return result > 0 ? "" : "删除失败";
            }
            catch (Exception ex)
            {
                return "删除失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 检查客户是否可以删除
        /// </summary>
        public bool CheckCompanyCanDelete(string compID, string companyNo)
        {
            // 这里可以添加检查逻辑，比如检查是否有关联数据
            return true;
        }

        /// <summary>
        /// 获取最大公司编号
        /// </summary>
        public string GetMaxCompanyID()
        {
            string sql = "SELECT TOP 1 CompID FROM T_CompanyInfo ORDER BY CompID DESC";

            try
            {
                DataTable dt = DBHelper.GetDataTable(sql, CommandType.Text, null);
                if (dt != null && dt.Rows.Count > 0)
                {
                    return dt.Rows[0]["CompID"].ToString();
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception("获取最大公司编号时出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取第一个客户信息（时间最老的）
        /// </summary>
        public DataTable GetFirstCustomerInfo(string companyNo)
        {
            string sql = @"
                SELECT TOP 1 CompID, CompName, NameJC, LegalMan, Phone, EMail,
                       REPLACE(CONVERT(varchar(19), InDate, 120), 'T', ' ') as InDate
                FROM T_CompanyInfo
                WHERE CompanyNo = @CompanyNo
                ORDER BY InDate ASC";

            SqlParameter[] parameters = {
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar, 10) { Value = companyNo }
            };

            try
            {
                return DBHelper.GetDataTable(sql, CommandType.Text, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception("获取第一个客户信息时出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 创建SQL参数数组
        /// </summary>
        private SqlParameter[] CreateParameters(CompanyInfo company)
        {
            return new SqlParameter[] {
                new SqlParameter("@CompID", SqlDbType.NVarChar) { Value = company.CompID },
                new SqlParameter("@CompName", SqlDbType.NVarChar) { Value = company.CompName },
                new SqlParameter("@NameJC", SqlDbType.NVarChar) { Value = (object)company.NameJC ?? DBNull.Value },
                new SqlParameter("@CompNameEN", SqlDbType.NVarChar) { Value = (object)company.CompNameEN ?? DBNull.Value },
                new SqlParameter("@LegalMan", SqlDbType.NVarChar) { Value = (object)company.LegalMan ?? DBNull.Value },
                new SqlParameter("@OrganizationCode", SqlDbType.NVarChar) { Value = (object)company.OrganizationCode ?? DBNull.Value },
                new SqlParameter("@Category", SqlDbType.NVarChar) { Value = (object)company.Category ?? DBNull.Value },
                new SqlParameter("@BusinessScope", SqlDbType.NVarChar) { Value = (object)company.BusinessScope ?? DBNull.Value },
                new SqlParameter("@RegAmount", SqlDbType.NVarChar) { Value = (object)company.RegAmount ?? DBNull.Value },
                new SqlParameter("@TaxNumber", SqlDbType.NVarChar) { Value = (object)company.TaxNumber ?? DBNull.Value },
                new SqlParameter("@PrdXKZ", SqlDbType.NVarChar) { Value = (object)company.PrdXKZ ?? DBNull.Value },
                new SqlParameter("@RegAddr", SqlDbType.NVarChar) { Value = (object)company.RegAddr ?? DBNull.Value },
                new SqlParameter("@CompAddr", SqlDbType.NVarChar) { Value = (object)company.CompAddr ?? DBNull.Value },
                new SqlParameter("@PAddr", SqlDbType.NVarChar) { Value = (object)company.PAddr ?? DBNull.Value },
                new SqlParameter("@PostalCode", SqlDbType.NVarChar) { Value = (object)company.PostalCode ?? DBNull.Value },
                new SqlParameter("@IsMember", SqlDbType.NVarChar) { Value = (object)company.IsMember ?? DBNull.Value },
                new SqlParameter("@CMan", SqlDbType.NVarChar) { Value = (object)company.CMan ?? DBNull.Value },
                new SqlParameter("@Sex", SqlDbType.NVarChar) { Value = (object)company.Sex ?? DBNull.Value },
                new SqlParameter("@Phone", SqlDbType.NVarChar) { Value = (object)company.Phone ?? DBNull.Value },
                new SqlParameter("@Fax", SqlDbType.NVarChar) { Value = (object)company.Fax ?? DBNull.Value },
                new SqlParameter("@EMail", SqlDbType.NVarChar) { Value = (object)company.EMail ?? DBNull.Value },
                new SqlParameter("@Website", SqlDbType.NVarChar) { Value = (object)company.Website ?? DBNull.Value },
                new SqlParameter("@APPID", SqlDbType.NVarChar) { Value = (object)company.APPID ?? DBNull.Value },
                new SqlParameter("@APPSECRET", SqlDbType.NVarChar) { Value = (object)company.APPSECRET ?? DBNull.Value },
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = company.CompanyNo },
                new SqlParameter("@Status", SqlDbType.NVarChar) { Value = company.Status },
                new SqlParameter("@InMan", SqlDbType.NVarChar) { Value = company.InMan },
                new SqlParameter("@InDate", SqlDbType.DateTime) { Value = company.InDate },
                new SqlParameter("@Remark", SqlDbType.NVarChar) { Value = (object)company.Remark ?? DBNull.Value }
            };
        }
    }
} 