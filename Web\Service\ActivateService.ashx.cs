using System;
using System.Collections;
using System.Data;
using System.Web;
using System.Web.SessionState;
using BLL;
using Common;
using Newtonsoft.Json;
using Model;

namespace Web.Service
{
    /// <summary>
    /// 产品激活服务处理类
    /// </summary>
    public class ActivateService : <PERSON><PERSON>ttpHandler, IRequiresSessionState
    {
        // 登录用户信息
        private string _loginUser;    // 登录账号
        private string _loginName;    // 登录用户全名
        private string _companyNo;    // 所属公司编号

        #region 请求处理
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];//获取操作的参数类型
            string DataParams = context.Request.Params["Data"];//获取传入的Data值

            switch (Operate)
            {
                case "GetFirstCustomer": // ��ȡ��һ���ͻ���Ϣ
                    GetFirstCustomer();
                    break;

                case "ActivateProduct": // �����Ʒ
                    if (string.IsNullOrEmpty(DataParams))
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error: ��Ч�Ĳ���" }));
                        return;
                    }
                    Result = ProcessActivation(DataParams);
                    context.Response.Write(Result);
                    break;

                default:
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error: ��Ч�Ĳ�������" }));
                    break;
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// ��¼״̬����ȡ�û���Ϣ
        /// </summary>
        private bool CheckLogin()
        {
            if (HttpContext.Current.Session["LoginName"] != null)
            {
                _loginUser = HttpContext.Current.Session["LoginName"].ToString();
                _loginName = HttpContext.Current.Session["FullName"].ToString();
                _companyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                return true;
            }
            return false;
        }

        /// <summary>
        /// ���л�����
        /// </summary>
        private Hashtable DeserializeParams(string jsonParams)
        {
            try
            {
                return JsonConvert.DeserializeObject<Hashtable>(jsonParams);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// ��ȡ����ֵ
        /// </summary>
        private T GetParamValue<T>(Hashtable paramObj, string key, T defaultValue = default)
        {
            if (paramObj == null || !paramObj.ContainsKey(key) || paramObj[key] == null)
                return defaultValue;

            try
            {
                // ������ַ������ͣ�ȥ��ǰ��ո�
                if (typeof(T) == typeof(string))
                {
                    string value = paramObj[key].ToString().Trim();
                    return value.Length == 0 ? defaultValue : (T)(object)value;
                }
                if (typeof(T) == typeof(bool))
                    return (T)(object)Convert.ToBoolean(paramObj[key]);
                if (typeof(T) == typeof(int))
                    return (T)(object)Convert.ToInt32(paramObj[key]);
                if (typeof(T) == typeof(decimal))
                    return (T)(object)Convert.ToDecimal(paramObj[key]);
                return (T)Convert.ChangeType(paramObj[key], typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        #endregion

        #region ҵ��������
        /// <summary>
        /// ��ȡ��һ���ͻ���Ϣ
        /// </summary>
        private void GetFirstCustomer()
        {
            string sReturn = string.Empty;

            try
            {
                // ������¼״̬��ֱ��ʹ��Ĭ�Ϲ�˾��Ż�����û�ȡ
                string defaultCompanyNo = "COMP001"; // ���Դ������ļ��������ط���ȡĬ�Ϲ�˾���
                
                CompanyInfo firstCustomer = CompanyPricingBll.GetFirstCustomerInfo(defaultCompanyNo);
                
                if (firstCustomer != null)
                {
                    var customerData = new
                    {
                        CompID = firstCustomer.CompID,
                        CompName = firstCustomer.CompName,
                        LegalMan = firstCustomer.LegalMan,
                        Phone = firstCustomer.Phone,
                        EMail = firstCustomer.EMail,
                        InDate = firstCustomer.InDate.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                    
                    sReturn = JsonConvert.SerializeObject(new { 
                        Msg = "Success", 
                        Data = customerData 
                    });
                }
                else
                {
                    sReturn = JsonConvert.SerializeObject(new { 
                        Msg = "NoData", 
                        Data = (object)null 
                    });
                }
            }
            catch (Exception ex)
            {
                sReturn = JsonConvert.SerializeObject(new { 
                    Msg = "Error: " + ex.Message, 
                    Data = (object)null 
                });
            }

            HttpContext.Current.Response.Write(sReturn);
        }

        /// <summary>
        /// ������Ʒ����
        /// </summary>
        private string ProcessActivation(string jsonParams)
        {
            try
            {
                var paramObj = DeserializeParams(jsonParams);
                if (paramObj == null)
                {
                    return JsonConvert.SerializeObject(new { Msg = "Error: ��Ч�Ĳ�����ʽ" });
                }

                string clientId = GetParamValue<string>(paramObj, "ClientId");
                string clientName = GetParamValue<string>(paramObj, "ClientName");
                string activationCode = GetParamValue<string>(paramObj, "ActivationCode");

                // ��֤�������
                if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientName) || string.IsNullOrEmpty(activationCode))
                {
                    return JsonConvert.SerializeObject(new { Msg = "Error: �ͻ�ID���ͻ����ƺͼ����벻��Ϊ��" });
                }

                // ����������Ӽ�������֤�߼�
                // ���磺��֤�������ʽ����鼤�����Ƿ���ʹ�õ�

                // ģ�⼤���
                // ʵ����Ŀ������Ӧ�õ�����Ӧ��BLL�������������߼�
                
                return JsonConvert.SerializeObject(new { 
                    Msg = "Success", 
                    Data = new { 
                        ClientId = clientId,
                        ClientName = clientName,
                        ActivationTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        Status = "�Ѽ���"
                    }
                });
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { Msg = "Error: " + ex.Message });
            }
        }
        #endregion

        public bool IsReusable
        {
            get { return false; }
        }
    }
}
