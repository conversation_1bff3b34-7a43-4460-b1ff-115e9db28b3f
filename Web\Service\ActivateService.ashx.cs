using System;
using System.Collections;
using System.Data;
using System.Web;
using BLL;
using Common;
using Newtonsoft.Json;
using Model;

namespace Web.Service
{
    /// <summary>
    /// 产品激活服务处理类
    /// </summary>
    public class ActivateService : IHttpHandler
    {

        #region 请求处理
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];//获取操作的参数类型
            string DataParams = context.Request.Params["Data"];//获取传入的Data值

            switch (Operate)
            {
                case "GetFirstCustomer": // 获取第一个客户信息
                    GetFirstCustomer();
                    break;

                case "ActivateProduct": // 激活产品
                    if (string.IsNullOrEmpty(DataParams))
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error: 无效的参数" }));
                        return;
                    }
                    Result = ProcessActivation(DataParams);
                    context.Response.Write(Result);
                    break;

                default:
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error: 无效的操作类型" }));
                    break;
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 检查登录状态并获取用户信息
        /// </summary>
        private bool CheckLogin()
        {
            if (HttpContext.Current.Session["LoginName"] != null)
            {
                _loginUser = HttpContext.Current.Session["LoginName"].ToString();
                _loginName = HttpContext.Current.Session["FullName"].ToString();
                _companyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 反序列化参数
        /// </summary>
        private Hashtable DeserializeParams(string jsonParams)
        {
            try
            {
                return JsonConvert.DeserializeObject<Hashtable>(jsonParams);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        private T GetParamValue<T>(Hashtable paramObj, string key, T defaultValue = default)
        {
            if (paramObj == null || !paramObj.ContainsKey(key) || paramObj[key] == null)
                return defaultValue;

            try
            {
                // 如果是字符串类型，去除前后空格
                if (typeof(T) == typeof(string))
                {
                    string value = paramObj[key].ToString().Trim();
                    return value.Length == 0 ? defaultValue : (T)(object)value;
                }
                if (typeof(T) == typeof(bool))
                    return (T)(object)Convert.ToBoolean(paramObj[key]);
                if (typeof(T) == typeof(int))
                    return (T)(object)Convert.ToInt32(paramObj[key]);
                if (typeof(T) == typeof(decimal))
                    return (T)(object)Convert.ToDecimal(paramObj[key]);
                return (T)Convert.ChangeType(paramObj[key], typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        #endregion

        #region 业务处理方法
        /// <summary>
        /// 获取第一个客户信息
        /// </summary>
        private void GetFirstCustomer()
        {
            string sReturn = string.Empty;

            try
            {
                // 不检查登录状态，直接使用默认公司编号或从配置获取
                string defaultCompanyNo = "COMP001"; // 可以从配置文件或其他地方获取默认公司编号
                
                CompanyInfo firstCustomer = CompanyPricingBll.GetFirstCustomerInfo(defaultCompanyNo);
                
                if (firstCustomer != null)
                {
                    var customerData = new
                    {
                        CompID = firstCustomer.CompID,
                        CompName = firstCustomer.CompName,
                        LegalMan = firstCustomer.LegalMan,
                        Phone = firstCustomer.Phone,
                        EMail = firstCustomer.EMail,
                        InDate = firstCustomer.InDate.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                    
                    sReturn = JsonConvert.SerializeObject(new { 
                        Msg = "Success", 
                        Data = customerData 
                    });
                }
                else
                {
                    sReturn = JsonConvert.SerializeObject(new { 
                        Msg = "NoData", 
                        Data = (object)null 
                    });
                }
            }
            catch (Exception ex)
            {
                sReturn = JsonConvert.SerializeObject(new { 
                    Msg = "Error: " + ex.Message, 
                    Data = (object)null 
                });
            }

            HttpContext.Current.Response.Write(sReturn);
        }

        /// <summary>
        /// 处理产品激活
        /// </summary>
        private string ProcessActivation(string jsonParams)
        {
            try
            {
                var paramObj = DeserializeParams(jsonParams);
                if (paramObj == null)
                {
                    return JsonConvert.SerializeObject(new { Msg = "Error: 无效的参数格式" });
                }

                string clientId = GetParamValue<string>(paramObj, "ClientId");
                string clientName = GetParamValue<string>(paramObj, "ClientName");
                string activationCode = GetParamValue<string>(paramObj, "ActivationCode");

                // 验证必填参数
                if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientName) || string.IsNullOrEmpty(activationCode))
                {
                    return JsonConvert.SerializeObject(new { Msg = "Error: 客户ID、客户名称和激活码不能为空" });
                }

                // 这里可以添加激活码验证逻辑
                // 例如：验证激活码格式、检查激活码是否已使用等

                // 模拟激活过程
                // 实际项目中应该调用相应的BLL层方法进行激活操作
                
                return JsonConvert.SerializeObject(new { 
                    Msg = "Success", 
                    Data = new { 
                        ClientId = clientId,
                        ClientName = clientName,
                        ActivationTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        Status = "已激活"
                    }
                });
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { Msg = "Error: " + ex.Message });
            }
        }
        #endregion

        public bool IsReusable
        {
            get { return false; }
        }
    }
}
