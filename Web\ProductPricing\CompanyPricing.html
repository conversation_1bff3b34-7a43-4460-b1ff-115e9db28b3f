<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>客户信息</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=21&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=21&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#Companylist',
                id: 'CompanylistID',
                url: '../Service/CompanyPricing.ashx?OP=GetCompanyList',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'CompID', title: '公司ID', width: 80, sort: true },
                    { field: 'CompName', title: '公司名称', width: 180, sort: true },
                    { field: 'NameJC', title: '公司简称', width: 100, sort: true },
                    { field: 'LegalMan', title: '法人代表', width: 70 },
                    { field: 'OrganizationCode', title: '组织机构代码', minWidth: 180 },
                    { field: 'Category', title: '注册类型', minWidth: 90 },
                    { field: 'BusinessScope', title: '经营范围', minWidth: 200 },
                    { field: 'RegAmount', title: '注册资金', minWidth: 70 },
                    { field: 'TaxNumber', title: '税务登记证号', minWidth: 180 },
                    { field: 'RegAddr', title: '注册地址', minWidth: 200 },
                    { field: 'CompAddr', title: '公司地址', width: 200 },
                    { field: 'PAddr', title: '生产地址', width: 200 },
                    { field: 'PostalCode', title: '邮政编码', width: 70 },
                    { field: 'CMan', title: '联系人', width: 70 },
                    { field: 'Phone', title: '电话', minWidth: 100 },
                    { field: 'Fax', title: '传真', minWidth: 100 },
                    { field: 'EMail', title: 'EMAIL', minWidth: 200 },
                    { field: 'APPID', title: 'APPID', minWidth: 200 },
                    { field: 'APPSECRET', title: 'APPSECRET', minWidth: 200 },
                    { field: 'Status', title: '状态', width: 80 },
                    { field: 'Remark', title: '备注', width: 200 },
                    { field: 'InMan', title: '录入人', width: 80 },
                    { field: 'InDate', title: '录入时间', width: 150 },
                    { field: 'op', title: '操作', width: 180, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(Companylist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(Companylist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(Companylist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(Companylist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    layer.confirm('您确实要删除该公司信息么？', function (index) {  // 2022088  删除功能不启用


                        //向服务端发送删除指令
                        var sCompID = data.CompID;
                        var Params = { CompID: sCompID };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/CompanyPricing.ashx?OP=DeleteCompany",
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('删除成功！');

                                    $('#CompanyBut_open').click();  // 重新查询


                                } else {
                                    layer.msg('删除失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('删除失败2，请重试！')
                            }
                        });

                    }); // 删除

                } else if (layEvent === 'edit') {
                    $('#head-title1').html("修改公司信息");
                    $('#txtAEFlag').val("2");

                    $("#txtCompID").val(data.CompID);
                    $("#txtCompName").val(data.CompName);
                    $("#txtNameJC").val(data.NameJC);
                    $("#txtCompNameEN").val(data.CompNameEN);
                    $("#txtLegalMan").val(data.LegalMan);
                    $("#txtOrganizationCode").val(data.OrganizationCode);
                    $("#txtCategory").val(data.Category);
                    $("#txtBusinessScope").val(data.BusinessScope);
                    $("#txtRegAmount").val(data.RegAmount);
                    $("#txtTaxNumber").val(data.TaxNumber);
                    $("#txtRegAddr").val(data.RegAddr);
                    $("#txtCompAddr").val(data.CompAddr);
                    $("#txtPAddr").val(data.PAddr);
                    $("#txtPostalCode").val(data.PostalCode);
                    $("#txtPrdXKZ").val(data.PrdXKZ);
                    $("#txtCMan").val(data.CMan);
                    $("#txtPhone").val(data.Phone);
                    $("#txtFax").val(data.Fax);
                    $("#txtEMail").val(data.EMail);
                    $("#txtWebsite").val(data.Website);
                    $("#txtAPPID").val(data.APPID);
                    $("#txtAPPSECRET").val(data.APPSECRET);
                    $("#txtRemark").val(data.Remark);

                    $("#txtCompID").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录


                } else if (layEvent === 'qy') {  // 启用

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                    $("#hint-value").addClass("XC-Font-Green")

                    //设置删除的标题
                    $("#hint-title").html("确定要启用该公司信息吗？公司编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.CompID)

                    $("#txtCompID").val(data.CompID)

                    $("#txtOPFlag").val("qy")

                } else if (layEvent === 'jy') {  // 禁用

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要禁用该公司信息吗？公司编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.CompID)

                    $("#txtCompID").val(data.CompID)

                    $("#txtOPFlag").val("jy")

                }




            });


            //  查询 -- 发号物料信息
            $('#CompanyBut_open').click(function () {
                var sCName = encodeURI($("#txtSCName").val());
                var sLMan = encodeURI($("#txtSLMan").val());  // 法人代表
                var sCategory = encodeURI($("#txtSCategory").val());  // 注册类型
                var sBDate = $("#txtSBDate").val();
                var sEDate = $("#txtSEDate").val();

                var Data = '';
                var Params = { CName: sCName, LMan: sLMan, Category: sCategory, BDate: sBDate, EDate: sEDate };
                var Data = JSON.stringify(Params);

                table.reload('CompanylistID', {
                    method: 'post',
                    url: '../Service/CompanyPricing.ashx?OP=GetCompanyList&Data=' + Data,
                    where: {
                        'No': sCName,
                        'name': sLMan
                    }, page: {
                        curr: 1
                    }
                });
            });


            //禁用\启用
            $("#CompanyOPBtn").click(function () {

                var Flag = $("#txtOPFlag").val()

                if (Flag == "jy") {
                    //向服务端发送禁用指令
                    var sCompID = $("#txtCompID").val()
                    var Params = { CompID: sCompID };
                    var Data = JSON.stringify(Params);
                    $.ajax({
                        type: "POST",
                        url: "../Service/CompanyPricing.ashx?OP=DisableCompany",
                        data: { Data: Data },
                        success: function (data) {
                            var parsedJson = jQuery.parseJSON(data);

                            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                layer.msg('禁用成功！');

                                $('#CompanyBut_open').click();  // 重新查询

                                closeDialog()

                            } else {
                                layer.msg('禁用失败，请重试！')
                            }
                        },
                        error: function (data) {
                            layer.msg('禁用失败2，请重试！')
                        }
                    });
                } else if (Flag == "qy") {
                    //向服务端发送启用指令
                    var sCompID = $("#txtCompID").val()
                    var Params = { CompID: sCompID };
                    var Data = JSON.stringify(Params);
                    $.ajax({
                        type: "POST",
                        url: "../Service/CompanyPricing.ashx?OP=EnableCompany",
                        data: { Data: Data },
                        success: function (data) {
                            var parsedJson = jQuery.parseJSON(data);

                            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                layer.msg('启用成功！');

                                $('#CompanyBut_open').click();  // 重新查询

                                closeDialog()

                            } else {
                                layer.msg('启用失败，请重试！')
                            }
                        },
                        error: function (data) {
                            layer.msg('启用失败2，请重试！')
                        }
                    });
                }
            })

            // 保存按钮事件
            $('#CompanyInfoSaveBtn').click(function () {
                // 验证必填字段
                var compID = $("#txtCompID").val().trim();
                var compName = $("#txtCompName").val().trim();
                var nameJC = $("#txtNameJC").val().trim();
                var flag = $("#txtAEFlag").val(); // 1=新增, 2=修改
                var isAdd = flag == "1";

                if (!isAdd && compID == "") {
                    layer.msg('公司ID不能为空！');
                    $("#txtCompID").focus();
                    return;
                }
                if (compName == "") {
                    layer.msg('公司名称不能为空！');
                    $("#txtCompName").focus();
                    return;
                }
                if (nameJC == "") {
                    layer.msg('公司简称不能为空！');
                    $("#txtNameJC").focus();
                    return;
                }

                var params = {
                    CompName: compName,
                    NameJC: nameJC,
                    CompNameEN: $("#txtCompNameEN").val(),
                    LegalMan: $("#txtLegalMan").val(),
                    OrganizationCode: $("#txtOrganizationCode").val(),
                    Category: $("#txtCategory").val(),
                    BusinessScope: $("#txtBusinessScope").val(),
                    RegAmount: $("#txtRegAmount").val(),
                    TaxNumber: $("#txtTaxNumber").val(),
                    PrdXKZ: $("#txtPrdXKZ").val(),
                    RegAddr: $("#txtRegAddr").val(),
                    CompAddr: $("#txtCompAddr").val(),
                    PAddr: $("#txtPAddr").val(),
                    PostalCode: $("#txtPostalCode").val(),
                    CMan: $("#txtCMan").val(),
                    Phone: $("#txtPhone").val(),
                    Fax: $("#txtFax").val(),
                    EMail: $("#txtEMail").val(),
                    Website: $("#txtWebsite").val(),
                    APPID: $("#txtAPPID").val(),
                    APPSECRET: $("#txtAPPSECRET").val(),
                    Remark: $("#txtRemark").val()
                };

                // 修改时需要包含CompID
                if (!isAdd) {
                    params.CompID = compID;
                }

                var data = JSON.stringify(params);
                var operation = isAdd ? "AddCompany" : "UpdateCompany";

                $.ajax({
                    type: "POST",
                    url: "../Service/CompanyPricing.ashx?OP=" + operation,
                    data: { Data: data },
                    success: function (result) {
                        var parsedJson = jQuery.parseJSON(result);
                        if (parsedJson != undefined && parsedJson.Msg == 'Success') {
                            layer.msg(isAdd ? '新增成功！' : '修改成功！');
                            $('#CompanyBut_open').click(); // 重新查询
                            closeDialog();
                        } else if (parsedJson.Msg == 'CompanyNameExists') {
                            layer.msg('公司名称已存在，请重新输入！');
                            $("#txtCompName").focus();
                        } else {
                            layer.msg('保存失败：' + parsedJson.Msg);
                        }
                    },
                    error: function () {
                        layer.msg('保存失败，请重试！');
                    }
                });
            });


        });




        function openDialog(n) {  // 新增
            $('#head-title1').html("新增公司基本信息");
            $('#txtAEFlag').val("1");

            $("#txtCompID").val("系统自动生成");
            $("#txtCompName").val("");
            $("#txtNameJC").val("");
            $("#txtCompNameEN").val("");
            $("#txtLegalMan").val("");
            $("#txtOrganizationCode").val("");
            $("#txtCategory").val("");
            $("#txtBusinessScope").val("");
            $("#txtRegAmount").val("");
            $("#txtTaxNumber").val("");
            $("#txtRegAddr").val("");
            $("#txtCompAddr").val("");
            $("#txtPAddr").val("");
            $("#txtPostalCode").val("");
            $("#txtPrdXKZ").val("");
            $("#txtCMan").val("");
            $("#txtPhone").val("");
            $("#txtFax").val("");
            $("#txtEMail").val("");
            $("#txtWebsite").val("");
            $("#txtAPPID").val("");
            $("#txtAPPSECRET").val("");
            $("#txtRemark").val("");

            $("#txtCompID").attr("disabled", "disabled");

            $("#div_warning").html("");
            $("#div_warning").hide();

            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }





    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">
        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table>tbody>tr>td {
            border: 0px;
        }

        /*.black_overlay {
                    display: none;
                    position: absolute;
                    top: 0%;
                    left: 0%;
                    width: 100%;
                    height: 100%;
                    background-color: #bbbcc7;
                    z-index: 1001;
                    -moz-opacity: 0.8;
                    opacity: .80;
                    filter: alpha(opacity=60);
                }
        */

        .wangid_conbox td,
        .wangid_conbox th,
        #ShowOne td {
            font-size: 12px
        }

        #ShowOne tr {
            height: 40px;
        }

        #ShowOne .XC-Input-block {
            width: 100%;
            margin-left: 5px
        }


        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }


        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }
    </style>




</head>

<body>
    <div class="div_find">

        <p>
            <label class="find_labela">公司名称</label> <input type="text" id="txtSCName" class="find_input" />
            <label class="find_labela">法人代表</label><input type="text" id="txtSLMan" class="find_input" />
            <label class="find_labela">注册类型</label><input type="text" id="txtSCategory" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="CompanyBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(1)">添加</button>

        </p>
        <p id="open" style="display:none;">
            <label class="find_labela" id="Label1">录入时间：</label><input type="date" id="txtSBDate" class="find_input" />-
            <input type="date" id="txtSEDate" class="find_input" />
        </p>
    </div>
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="Companylist" lay-filter="Companylist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            {{# if(d.Status === '启用'){ }}
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="jy">禁用</button>
            {{# } else { }}
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="qy">启用</button>
            {{# } }}
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>


    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1" id="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="20" cellpadding="20" border='0' style="width:99%; ">
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            公司ID<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtCompID" name="txtCompID" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            公司名称<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtCompName" name="txtCompName" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            公司简称<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtNameJC" name="txtNameJC" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            英文名称
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtCompNameEN" name="txtCompNameEN" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            法人代表
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtLegalMan" name="txtLegalMan" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            组织机构代码
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtOrganizationCode"
                                name="txtOrganizationCode" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            注册类型
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtCategory" name="txtCategory" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            注册资金
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtRegAmount" name="txtRegAmount" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            经营范围
                        </td>
                        <td colspan="4">
                            <textarea class="XC-Input-block" id="txtBusinessScope" name="txtBusinessScope"
                                style="height:60px"> </textarea>
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            税务登记证号
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtTaxNumber" name="txtTaxNumber" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            联系人
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtCMan" name="txtCMan" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            注册地址
                        </td>
                        <td colspan=3>
                            <input type="text" class="XC-Input-block" id="txtRegAddr" name="txtRegAddr" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            公司地址
                        </td>
                        <td colspan=3>
                            <input type="text" class="XC-Input-block" id="txtCompAddr" name="txtCompAddr" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            生产地址
                        </td>
                        <td colspan=3>
                            <input type="text" class="XC-Input-block" id="txtPAddr" name="txtPAddr" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            生产企业许可证
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtPrdXKZ" name="txtPrdXKZ" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            邮政编码
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtPostalCode" name="txtPostalCode" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            传真
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtFax" name="txtFax" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            电话
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtPhone" name="txtPhone" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            网址
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtWebsite" name="txtWebsite" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            EMAIL
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtEMail" name="txtEMail" />
                        </td>
                    </tr>

                    <tr>
                        <td style=" width:120px; text-align:right;">
                            备注
                        </td>
                        <td colspan="4">
                            <textarea class="XC-Input-block" id="txtRemark" name="txtRemark"
                                style="height:60px"> </textarea>
                        </td>
                    </tr>
                    <tr>
                        <td colspan=6>
                            <hr style=" background-color: #E5E5E5; height: 1px;" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:120px; text-align:right;">
                            APPID
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtAPPID" name="txtAPPID" />
                        </td>
                        <td style=" width:120px; text-align:right;">
                            APPSECRET
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtAPPSECRET" name="txtAPPSECRET" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="CompanyInfoSaveBtn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="CompanySaveClose"
                    onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtCompID" name="txtCompID" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtOPFlag" name="txtOPFlag" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="CompanyOPBtn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>

</html>