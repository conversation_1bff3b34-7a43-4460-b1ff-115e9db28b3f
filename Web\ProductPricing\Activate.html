<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>产品激活与结算</title>
    <link href="../css/XC.css" rel="stylesheet" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" />
    <!-- 更新jQuery版本以兼容Bootstrap -->
    <script src="../js/jquery-3.6.0.min.js"></script>
    <script src="../js/json2.js" type="text/javascript"></script>
    <!-- 确保layer和laydate资源正确加载 -->
    <link href="../js/skins/default/laydate.css" rel="stylesheet" />
    <script src="../js/layer.js"></script>
    <script src="../js/laydate.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <style>
        html, body {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            background-color: #f5f9f5;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        .container {
            width: 100%;
            height: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        .header {
            background-color: #06891b;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header h2 {
            margin: 0;
            font-size: 20px;
        }

        .back-btn {
            background-color: transparent;
            border: 1px solid white;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background-color: white;
            color: #06891b;
        }

        .content {
            background-color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab:hover {
            color: #06891b;
        }

        .tab.active {
            color: #06891b;
            border-bottom: 3px solid #06891b;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }
        
        .form-group {
            margin-bottom: 20px;
            padding: 0 15px;
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
            box-sizing: border-box;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }

        select.form-control {
            height: 40px;
        }

        .form-control:focus {
            border-color: #06891b;
            outline: none;
            box-shadow: 0 0 5px rgba(6, 137, 27, 0.3);
        }

        .activation-code {
            margin-top: 30px;
            flex: 0 0 100%;
            max-width: 100%;
        }

        .activation-code textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn-container {
            text-align: center;
            margin-top: 20px;
            width: 100%;
        }

        .btn-primary {
            background-color: #06891b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .btn-primary:hover {
            background-color: #056b15;
        }

        .date-picker {
            position: relative;
            width: 100%;
        }

        .date-picker input {
            padding-right: 10px;
            cursor: pointer;
        }

        .date-picker:after {
            content: "";
            display: none;
        }

        .settlement-placeholder {
            text-align: center;
            padding: 50px 0;
            color: #999;
        }

        .settlement-placeholder i {
            font-size: 60px;
            margin-bottom: 20px;
            display: block;
        }
        
        /* 响应式样式 */
        @media (max-width: 992px) {
            .form-group {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
        
        @media (max-width: 768px) {
            .form-group {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
            }
            
            .header h2 {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>产品激活与结算管理</h2>
            <button class="back-btn" onclick="goBack()">返回登录</button>
        </div>
        <div class="content">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('activation')">激活/续费</div>
                <div class="tab" onclick="switchTab('settlement')">结算</div>
            </div>
            
            <div id="activation" class="tab-content active">
                <div class="form-row">
                    <div class="form-group">
                        <label for="clientId">客户ID</label>
                        <input type="text" class="form-control" id="clientId" placeholder="请输入客户ID">
                    </div>

                    <div class="form-group">
                        <label for="clientName">客户名称</label>
                        <input type="text" class="form-control" id="clientName" placeholder="请输入客户名称">
                    </div>

                    <div class="form-group">
                        <!-- 预留第三列的空位，保持每行三个字段 -->
                    </div>

                    <div class="form-group activation-code">
                        <label for="activationCode">激活/续费码</label>
                        <textarea class="form-control" id="activationCode" placeholder="请输入激活码或续费码"></textarea>
                    </div>
                </div>
                
                <div class="btn-container">
                    <button class="btn-primary" onclick="activateProduct()">确认激活</button>
                </div>
            </div>
            
            <div id="settlement" class="tab-content">
                <div class="settlement-placeholder">
                    <i>⚙️</i>
                    <p>结算功能正在开发中，敬请期待...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        $(function() {
            // 页面加载完成后的初始化操作
            console.log('激活页面初始化完成');
        });
        
        // 切换标签页
        function switchTab(tabId) {
            $('.tab').removeClass('active');
            $('.tab-content').removeClass('active');
            
            if (tabId === 'activation') {
                $('.tab').eq(0).addClass('active');
                $('#activation').addClass('active');
            } else if (tabId === 'settlement') {
                $('.tab').eq(1).addClass('active');
                $('#settlement').addClass('active');
            }
        }
        
        // 返回登录页
        function goBack() {
            window.location.href = "../Login.htm";
        }
        
        // 激活产品
        function activateProduct() {
            var clientId = $('#clientId').val();
            var clientName = $('#clientName').val();
            var activationCode = $('#activationCode').val();

            // 验证表单
            if (!clientId || !clientName || !activationCode) {
                alert('请填写完整的激活信息');
                return;
            }

            // 这里添加激活逻辑
            alert('产品激活请求已提交，请等待系统处理');
        }
    </script>
</body>
</html>